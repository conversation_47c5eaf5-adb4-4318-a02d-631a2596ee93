# Tag Permissions Inheritance

![License](https://img.shields.io/badge/license-MIT-blue.svg) [![Latest Stable Version](https://img.shields.io/packagist/v/fargot132/flarum-tag-permissions.svg)](https://packagist.org/packages/fargot132/flarum-tag-permissions) [![Total Downloads](https://img.shields.io/packagist/dt/fargot132/flarum-tag-permissions.svg)](https://packagist.org/packages/fargot132/flarum-tag-permissions)

A [Flarum](https://flarum.org) extension that enables automatic tag permissions inheritance.

## Features

- **Automatic Permission Inheritance**: Child tags automatically inherit permissions from their parent tags
- **Recursive Inheritance**: Permissions cascade through multiple levels of tag hierarchy
- **Seamless Integration**: Works transparently with existing Flarum tag permissions

## How It Works

When a user tries to access a child tag, the extension checks:

1. **Direct Permissions**: Does the user have explicit permissions on the child tag?
2. **Parent Permissions**: If not, does the user have permissions on the parent tag?
3. **Recursive Check**: Continues up the hierarchy until a permission is found or the root is reached
4. **Global Permissions**: Falls back to global permissions for unrestricted tags

## Installation

Install with composer:

```sh
composer require fargot132/flarum-tag-permissions:"*"
```

## Updating

```sh
composer update fargot132/flarum-tag-permissions:"*"
php flarum migrate
php flarum cache:clear
```

## Usage

1. **Enable the Extension**: Activate the extension in your Flarum admin panel
2. **Set Parent Tag Permissions**: Configure permissions on parent tags as usual
3. **Create Child Tags**: Child tags will automatically inherit parent permissions

## Tag Hierarchy Features

- **Automatic Inheritance**: No configuration needed - inheritance works automatically

## Permissions Affected

This extension affects all tag-scoped permissions including:
- `startDiscussion` - Start discussions in tag
- `viewDiscussions` - View discussions in tag
- `reply` - Reply to discussions in tag
- `editPosts` - Edit posts in tag
- `deletePosts` - Delete posts in tag
- And any other tag-scoped permissions added by other extensions

## Links

- [Packagist](https://packagist.org/packages/fargot132/flarum-tag-permissions)
- [GitHub](https://github.com/fargot132/flarum-tag-permissions)
