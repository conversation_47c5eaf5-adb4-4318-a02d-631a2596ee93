<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

namespace Fargot132\TagPermissions;

use Flarum\Extend;
use Flarum\Tags\Tag;
use Flarum\Tags\Api\Serializer\TagSerializer;
use Fargot132\TagPermissions\Access\TagPermissionInheritancePolicy;
use Fargot132\TagPermissions\Api\Serializer\TagSerializerExtension;

return [
    (new Extend\Frontend('forum'))
        ->js(__DIR__.'/js/dist/forum.js')
        ->css(__DIR__.'/less/forum.less'),

    (new Extend\Frontend('admin'))
        ->js(__DIR__.'/js/dist/admin.js')
        ->css(__DIR__.'/less/admin.less'),

    (new Extend\Frontend('common'))
        ->js(__DIR__.'/js/dist/common.js'),

    new Extend\Locales(__DIR__.'/locale'),

    // Replace the default TagPolicy with our inheritance-aware policy
    (new Extend\Policy())
        ->modelPolicy(Tag::class, TagPermissionInheritancePolicy::class),
];
