<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

namespace Fargot132\TagPermissions\Access;

use Flarum\Tags\Tag;
use Flarum\User\Access\AbstractPolicy;
use Flarum\User\User;
use Fargot132\TagPermissions\Service\TagPermissionInheritanceService;

class TagPermissionInheritancePolicy extends AbstractPolicy
{
    protected TagPermissionInheritanceService $inheritanceService;

    public function __construct(TagPermissionInheritanceService $inheritanceService)
    {
        $this->inheritanceService = $inheritanceService;
    }

    public function can(User $actor, string $ability, Tag $tag)
    {
        $result = $this->inheritanceService->canWithInheritance($actor, $ability, $tag);

        return $result ? $this->allow() : $this->deny();
    }

    public function addToDiscussion(User $actor, Tag $tag)
    {
        return $actor->can('startDiscussion', $tag);
    }
}
