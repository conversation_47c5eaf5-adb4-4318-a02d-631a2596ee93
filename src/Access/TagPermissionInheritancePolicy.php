<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

namespace Fargot132\TagPermissions\Access;

use Flarum\Tags\Tag;
use Flarum\User\Access\AbstractPolicy;
use Flarum\User\User;

class TagPermissionInheritancePolicy extends AbstractPolicy
{
    public function can(User $actor, string $ability, Tag $tag)
    {
        // Admin users have all permissions
        if ($actor->isAdmin()) {
            return $this->allow();
        }

        // First check if the tag has explicit permissions
        if ($tag->is_restricted) {
            $id = $tag->id;

            // Check direct permission on this tag
            if ($actor->hasPermission("tag$id.$ability")) {
                return $this->allow();
            }
        }

        // Always check parent permissions recursively for inheritance
        if ($tag->parent_id !== null && $tag->parent) {
            $parentResult = $this->checkParentPermissions($actor, $ability, $tag->parent);
            if ($parentResult !== null) {
                return $parentResult;
            }
        }

        // If tag is not restricted, check global permissions
        if (!$tag->is_restricted) {
            return $actor->hasPermission($ability) ? $this->allow() : null;
        }

        // If tag is restricted but user has no permission, deny
        return $this->deny();
    }

    /**
     * Recursively check parent tag permissions
     */
    protected function checkParentPermissions(User $actor, string $ability, Tag $parentTag): ?bool
    {
        // Check if parent has explicit permissions
        if ($parentTag->is_restricted) {
            $parentId = $parentTag->id;

            if ($actor->hasPermission("tag$parentId.$ability")) {
                return $this->allow();
            }
        }

        // If parent has a parent, check recursively
        if ($parentTag->parent_id !== null && $parentTag->parent) {
            return $this->checkParentPermissions($actor, $ability, $parentTag->parent);
        }

        // If parent is not restricted, check global permissions
        if (!$parentTag->is_restricted) {
            return $actor->hasPermission($ability) ? $this->allow() : null;
        }

        // If parent is restricted but user has no permission, deny access
        return $this->deny();
    }

    public function addToDiscussion(User $actor, Tag $tag)
    {
        return $actor->can('startDiscussion', $tag);
    }
}
