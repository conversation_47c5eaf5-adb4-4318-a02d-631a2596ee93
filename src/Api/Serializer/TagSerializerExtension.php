<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

namespace Fargot132\TagPermissions\Api\Serializer;

use Flarum\Api\Serializer\TagSerializer;
use Flarum\Tags\Tag;

class TagSerializerExtension
{
    public function __invoke(TagSerializer $serializer, $tag, array $attributes): array
    {
        if (!($tag instanceof Tag)) {
            return $attributes;
        }

        $actor = $serializer->getActor();

        // Override the canStartDiscussion and canAddToDiscussion attributes with inheritance logic
        $attributes['canStartDiscussion'] = $this->canWithInheritance($actor, 'startDiscussion', $tag);
        $attributes['canAddToDiscussion'] = $this->canWithInheritance($actor, 'addToDiscussion', $tag);

        return $attributes;
    }

    protected function canWithInheritance($actor, string $ability, Tag $tag): bool
    {
        // Admin users have all permissions
        if ($actor->isAdmin()) {
            return true;
        }

        // Check if the tag has explicit permissions
        if ($tag->is_restricted) {
            $id = $tag->id;

            // Check direct permission on this tag
            if ($actor->hasPermission("tag$id.$ability")) {
                return true;
            }
        }

        // Check parent permissions recursively for inheritance
        if ($tag->parent_id !== null && $tag->parent) {
            $parentResult = $this->checkParentPermissions($actor, $ability, $tag->parent);
            if ($parentResult === true) {
                return true;
            }
            if ($parentResult === false) {
                return false;
            }
            // If parentResult is null, continue checking
        }

        // If tag is not restricted, check global permissions
        if (!$tag->is_restricted) {
            return $actor->hasPermission($ability);
        }

        // If tag is restricted but user has no permission and no parent inheritance, deny
        return false;
    }

    protected function checkParentPermissions($actor, string $ability, Tag $parentTag): ?bool
    {
        // Check if parent has explicit permissions
        if ($parentTag->is_restricted) {
            $parentId = $parentTag->id;

            if ($actor->hasPermission("tag$parentId.$ability")) {
                return true; // Allow access
            }
        }

        // If parent has a parent, check recursively
        if ($parentTag->parent_id !== null && $parentTag->parent) {
            return $this->checkParentPermissions($actor, $ability, $parentTag->parent);
        }

        // If parent is not restricted, check global permissions
        if (!$parentTag->is_restricted) {
            return $actor->hasPermission($ability) ? true : null;
        }

        // If parent is restricted but user has no permission, deny access
        return false;
    }
}
