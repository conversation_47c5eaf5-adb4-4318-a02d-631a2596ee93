<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

namespace Fargot132\TagPermissions\Api\Serializer;

use Flarum\Tags\Api\Serializer\TagSerializer;
use Flarum\Tags\Tag;
use Fargot132\TagPermissions\Service\TagPermissionInheritanceService;

class TagSerializerExtension
{
    protected TagPermissionInheritanceService $inheritanceService;

    public function __construct(TagPermissionInheritanceService $inheritanceService)
    {
        $this->inheritanceService = $inheritanceService;
    }

    public function __invoke(TagSerializer $serializer, $tag, array $attributes): array
    {
        if (!($tag instanceof Tag)) {
            return $attributes;
        }

        $actor = $serializer->getActor();

        // Override the canStartDiscussion and canAddToDiscussion attributes with inheritance logic
        $attributes['canStartDiscussion'] = $this->inheritanceService->canWithInheritance($actor, 'startDiscussion', $tag);
        $attributes['canAddToDiscussion'] = $this->inheritanceService->canWithInheritance($actor, 'addToDiscussion', $tag);

        return $attributes;
    }


}
