<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

namespace Fargot132\TagPermissions\Listener;

use <PERSON>larum\Api\Controller\ShowForumController;
use Flarum\Http\RequestUtil;
use Flarum\Tags\Tag;
use Psr\Http\Message\ServerRequestInterface;

class LoadForumTagsWithChildren
{
    /**
     * @param ShowForumController $controller
     * @param $data
     * @param ServerRequestInterface $request
     */
    public function __invoke(ShowForumController $controller, &$data, ServerRequestInterface $request)
    {
        $actor = RequestUtil::getActor($request);

        // Load child tags that the user has permission to see
        $childTags = Tag::query()
            ->whereNotNull('parent_id')  // Only child tags
            ->whereHasPermission($actor, 'viewForum')  // User has permission
            ->withStateFor($actor)
            ->get();

        // Add child tags to the existing tags collection
        if (isset($data['tags']) && $childTags->isNotEmpty()) {
            $data['tags'] = $data['tags']->merge($childTags);
        }
    }
}
