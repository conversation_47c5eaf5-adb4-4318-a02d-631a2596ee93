<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

namespace Fargot132\TagPermissions\Tests\Unit;

use Fargot132\TagPermissions\Service\TagPermissionInheritanceService;
use PHPUnit\Framework\TestCase;
use Mockery as m;
use Flarum\User\User;
use Flarum\Tags\Tag;

class TagPermissionInheritanceServiceTest extends TestCase
{
    protected function tearDown(): void
    {
        m::close();
    }

    public function testAdminUserHasAllPermissions(): void
    {
        $service = new TagPermissionInheritanceService();

        $user = m::mock(User::class);
        $user->shouldReceive('isAdmin')->andReturn(true);

        $tag = m::mock(Tag::class);

        $result = $service->canWithInheritance($user, 'startDiscussion', $tag);

        $this->assertTrue($result);
    }

    public function testChildTagInheritsFromParent(): void
    {
        $service = new TagPermissionInheritanceService();

        $user = m::mock(User::class);
        $user->shouldReceive('isAdmin')->andReturn(false);
        $user->shouldReceive('hasPermission')->with('tag1.startDiscussion')->andReturn(true);
        $user->shouldReceive('hasPermission')->with('tag2.startDiscussion')->andReturn(false);

        $parentTag = m::mock(Tag::class);
        $parentTag->shouldReceive('getAttribute')->with('is_restricted')->andReturn(true);
        $parentTag->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $parentTag->shouldReceive('getAttribute')->with('parent_id')->andReturn(null);
        $parentTag->shouldReceive('setAttribute')->andReturn(null);
        $parentTag->parent_id = null;
        $parentTag->parent = null;
        $parentTag->is_restricted = true;
        $parentTag->id = 1;

        $childTag = m::mock(Tag::class);
        $childTag->shouldReceive('getAttribute')->with('is_restricted')->andReturn(true);
        $childTag->shouldReceive('getAttribute')->with('id')->andReturn(2);
        $childTag->shouldReceive('getAttribute')->with('parent_id')->andReturn(1);
        $childTag->shouldReceive('getAttribute')->with('parent')->andReturn($parentTag);
        $childTag->shouldReceive('setAttribute')->andReturn(null);
        $childTag->parent_id = 1;
        $childTag->parent = $parentTag;
        $childTag->is_restricted = true;
        $childTag->id = 2;

        $result = $service->canWithInheritance($user, 'startDiscussion', $childTag);

        $this->assertTrue($result);
    }

    public function testRestrictedChildTagDeniedWhenParentDenied(): void
    {
        $service = new TagPermissionInheritanceService();

        $user = m::mock(User::class);
        $user->shouldReceive('isAdmin')->andReturn(false);
        $user->shouldReceive('hasPermission')->with('tag1.startDiscussion')->andReturn(false);
        $user->shouldReceive('hasPermission')->with('tag2.startDiscussion')->andReturn(false);

        $parentTag = m::mock(Tag::class);
        $parentTag->shouldReceive('getAttribute')->with('is_restricted')->andReturn(true);
        $parentTag->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $parentTag->shouldReceive('getAttribute')->with('parent_id')->andReturn(null);
        $parentTag->shouldReceive('setAttribute')->andReturn(null);
        $parentTag->parent_id = null;
        $parentTag->parent = null;
        $parentTag->is_restricted = true;
        $parentTag->id = 1;

        $childTag = m::mock(Tag::class);
        $childTag->shouldReceive('getAttribute')->with('is_restricted')->andReturn(true);
        $childTag->shouldReceive('getAttribute')->with('id')->andReturn(2);
        $childTag->shouldReceive('getAttribute')->with('parent_id')->andReturn(1);
        $childTag->shouldReceive('getAttribute')->with('parent')->andReturn($parentTag);
        $childTag->shouldReceive('setAttribute')->andReturn(null);
        $childTag->parent_id = 1;
        $childTag->parent = $parentTag;
        $childTag->is_restricted = true;
        $childTag->id = 2;

        $result = $service->canWithInheritance($user, 'startDiscussion', $childTag);

        $this->assertFalse($result);
    }

    public function testServiceClassExists()
    {
        $service = new TagPermissionInheritanceService();
        $this->assertInstanceOf(TagPermissionInheritanceService::class, $service);
    }
}
